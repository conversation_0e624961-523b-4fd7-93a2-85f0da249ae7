"use client";

import { useState, useEffect, useCallback } from "react";
import { useUser } from "@stackframe/stack";
import { searchTags } from "@/app/actions/tags";
import { Input } from "@/components/ui/input";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { DateTimePicker } from "@/components/ui/datetime-picker";
import { TagPill } from "@/components/ui/tag-pill";
import { InlineTagPicker } from "@/components/ui/inline-tag-picker";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { ChevronDown, Check, ChevronLeft } from "lucide-react";
import type { Tag, Task, TaskSortOption } from "@/lib/db";
import {
  useTagsQuery,
  useAddTaskMutation,
  useCreateTagMutation,
  useListsQuery,
  useTaskCountsQuery
} from "@/lib/queries";
import { getContrastTextColor } from "@/lib/list-colors";

interface AddTaskDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTaskAdded: (newTask?: Task) => void;
  listId: string | null;
  sortOption?: TaskSortOption;
}

export function AddTaskDialog({
  open,
  onOpenChange,
  onTaskAdded,
  listId,
  sortOption = "position",
}: AddTaskDialogProps) {
  const user = useUser();
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [dueDate, setDueDate] = useState<Date | undefined | null>(undefined);
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);
  const [selectedListId, setSelectedListId] = useState<string>(listId || "");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  // TanStack Query hooks
  const { data: availableTags = [] } = useTagsQuery(user?.id || "");
  const { data: availableLists = [] } = useListsQuery(user?.id || "");
  const { data: taskCounts = {} } = useTaskCountsQuery(user?.id || "");
  const addTaskMutation = useAddTaskMutation(selectedListId || "", sortOption);
  const createTagMutation = useCreateTagMutation(user?.id || "");

  // Reset selected list when dialog opens or listId changes
  useEffect(() => {
    if (open && listId) {
      setSelectedListId(listId);
    }
  }, [open, listId]);

  const handleTagSelect = (tag: Tag) => {
    setSelectedTags(prev => {
      // Prevent duplicate selection
      if (prev.some(selectedTag => selectedTag.id === tag.id)) {
        console.log("Tag already selected:", tag.name);
        return prev;
      }
      return [...prev, tag];
    });
  };

  const handleTagRemove = (tag: Tag) => {
    setSelectedTags(prev => prev.filter(t => t.id !== tag.id));
  };

  const handleTagCreate = async (name: string, color: string): Promise<Tag | null> => {
    if (!user) return null;

    try {
      const newTag = await createTagMutation.mutateAsync({ name, color });
      // TanStack Query automatically updates the tags cache
      return newTag;
    } catch (error) {
      console.error('Error creating tag:', error);
      return null;
    }
  };

  const handleSearchTags = useCallback(async (searchTerm: string): Promise<Tag[]> => {
    if (!user) return [];

    try {
      return await searchTags(user.id, searchTerm);
    } catch (error) {
      console.error('Error searching tags:', error);
      return [];
    }
  }, [user]);

  const handleListSelect = (newListId: string) => {
    setSelectedListId(newListId);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title.trim()) {
      setError("Title is required");
      return;
    }

    if (!user) {
      setError("You must be logged in to add a task");
      return;
    }

    if (!listId) {
      setError("No list selected");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      const result = await addTaskMutation.mutateAsync({
        userId: user.id,
        title: title.trim(),
        description: description.trim() || undefined,
        due_date: dueDate || undefined,
        tagIds: selectedTags.map(tag => tag.id),
      });

      if (result) {
        // Reset form and close dialog
        setTitle("");
        setDescription("");
        setDueDate(undefined);
        setSelectedTags([]);
        setSelectedListId(listId || ""); // Reset to original list
        onOpenChange(false);
        // Pass the new task data for optimistic updates
        onTaskAdded(result);
      } else {
        setError("Failed to create task. Please try again.");
      }
    } catch (err) {
      console.error("Error creating task:", err);
      setError("An error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Reset form state when dialog is closed
    setTitle("");
    setDescription("");
    setDueDate(undefined);
    setSelectedTags([]);
    setSelectedListId(listId || ""); // Reset to original list
    setError("");
    onOpenChange(false);
  };

  return (
    <MobileDialog open={open} onOpenChange={handleClose}>
      <MobileDialogContent className="sm:max-w-[425px]" fullHeight>
        <MobileDialogHeader className="flex items-start justify-between px-4 pt-4 pb-0 md:px-0">
          <VisuallyHidden asChild>
            <MobileDialogTitle>New Task</MobileDialogTitle>
          </VisuallyHidden>

          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-6 w-6" />
            <span className="sr-only">Back</span>
          </Button>

          {/* Move to List Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="h-8 px-3 text-sm font-medium"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                {availableLists.find(list => list.id === selectedListId)?.name || "Select List"}
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="start"
              onClick={(e) => e.stopPropagation()}
              className="max-h-[200px] overflow-y-auto z-[80]"
            >
              {availableLists
                .map((list) => {
                  const hasColor = list.color !== null;
                  const taskCount = taskCounts?.[list.id] || 0;

                  // Get hover styles for list color with !important to override defaults
                  const getHoverStyles = (): React.CSSProperties => {
                    if (!hasColor) {
                      return {
                        '--hover-bg': '#f8f9fa',
                        '--hover-color': '#4b5563', // Brighter text color
                      } as React.CSSProperties;
                    }

                    const textColor = getContrastTextColor(list.color);
                    return {
                      '--hover-bg': list.color!,
                      '--hover-color': textColor,
                    } as React.CSSProperties;
                  };

                  const getBadgeStyles = () => {
                    if (!hasColor || !list.color) {
                      return {
                        backgroundColor: "#e9ecef",
                        color: "#4b5563", // Brighter text color
                        opacity: 0.8,
                      };
                    }

                    const textColor = getContrastTextColor(list.color);
                    return {
                      backgroundColor: list.color,
                      color: textColor,
                      opacity: 0.8,
                    };
                  };

                  return (
                    <DropdownMenuItem
                      key={list.id}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleListSelect(list.id);
                      }}
                      className="flex items-center gap-2 px-3 py-2 cursor-pointer transition-colors [&:hover]:bg-[var(--hover-bg)] [&:hover]:text-[var(--hover-color)]"
                      style={getHoverStyles() as React.CSSProperties}
                    >
                      {list.id === selectedListId && (
                        <Check className="h-4 w-4 text-current" />
                      )}
                      <span className="truncate max-w-[120px] font-medium text-sm">
                        {list.name}
                      </span>
                      {taskCount > 0 && (
                        <Badge
                          className="text-xs border-transparent"
                          style={getBadgeStyles()}
                        >
                          {taskCount}
                        </Badge>
                      )}
                    </DropdownMenuItem>
                  );
                })}
              {availableLists.length === 0 && (
                <DropdownMenuItem disabled>
                  No lists available
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </MobileDialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 px-4 pb-4 md:px-0 min-w-0">
            <div className="grid gap-2 min-w-0">
              <Input
                id="title"
                type="search"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                onKeyDown={(e) => {
                  // Prevent Enter from submitting the form
                  if (e.key === 'Enter') {
                    e.preventDefault();
                  }
                }}
                placeholder="Task title"
                autoComplete="off"
                spellCheck="false"
              />
            </div>

            <div className="grid gap-2 min-w-0">
              <RichTextEditor
                id="description"
                value={description}
                onChange={setDescription}
                placeholder="Add details about this task (optional)"
              />
            </div>

            <div className="grid gap-2 min-w-0">
              <DateTimePicker
                date={dueDate}
                setDate={setDueDate}
                placeholder="Select due date"
                includeTime={true}
              />
            </div>

            <div className="grid gap-2">
              {/* Tags Display with Inline Add Button */}
              <div className="flex flex-wrap gap-1.5">
                {selectedTags.map((tag) => (
                  <TagPill
                    key={tag.id}
                    tag={tag}
                    onRemove={() => handleTagRemove(tag)}
                    size="sm"
                    showRemove={true}
                    allowInlineEdit={false}
                  />
                ))}

                {/* Inline Tag Picker for Adding New Tags */}
                <InlineTagPicker
                  selectedTags={selectedTags}
                  availableTags={availableTags}
                  onTagSelect={handleTagSelect}
                  onTagRemove={handleTagRemove}
                  onTagCreate={handleTagCreate}
                  onSearchTags={handleSearchTags}
                  size="sm"
                />
              </div>
            </div>

            {error && (
              <p className="text-sm font-medium text-destructive">{error}</p>
            )}
          </div>

          <div className="flex justify-end px-4 pb-4">
            <button
              type="submit"
              disabled={isSubmitting}
              className="text-sm font-semibold text-muted-foreground hover:text-foreground disabled:text-muted-foreground/50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? "Creating..." : "Create"}
            </button>
          </div>
        </form>
      </MobileDialogContent>
    </MobileDialog>
  );
}
