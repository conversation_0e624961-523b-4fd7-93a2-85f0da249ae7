"use client";

import { usePathname } from "next/navigation";
import { Navigation } from "@/components/navigation";
import { BottomNavigation } from "@/components/bottom-navigation";
import { FloatingMascot } from "@/components/floating-mascot";
import { ListColorProvider } from "@/contexts/list-color-context";
import { PrimaryColorProvider } from "@/contexts/primary-color-context";
import { TagFilterProvider } from "@/contexts/tag-filter-context";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { useAppInitialization } from "@/hooks/use-app-initialization";

interface ClientLayoutProps {
  children: React.ReactNode;
  mascotPreference: "golden" | "black";
}

export function ClientLayout({
  children,
  mascotPreference
}: ClientLayoutProps) {
  const pathname = usePathname();
  const isTasksPage = pathname === "/tasks";

  // Initialize app with critical data preloading as early as possible
  useAppInitialization();

  return (
    <ErrorBoundary>
      <PrimaryColorProvider>
        <ListColorProvider>
          <TagFilterProvider>
            <div className="flex min-h-screen flex-col">
            {!isTasksPage && <Navigation />}
            <main className="flex-1 pb-16 md:pb-0">
              <div className="container-max-width">
                <ErrorBoundary>
                  {children}
                </ErrorBoundary>
              </div>
            </main>
            <BottomNavigation />
            <FloatingMascot
              defaultMascot={mascotPreference}
            />
            </div>
          </TagFilterProvider>
        </ListColorProvider>
      </PrimaryColorProvider>
    </ErrorBoundary>
  );
}
