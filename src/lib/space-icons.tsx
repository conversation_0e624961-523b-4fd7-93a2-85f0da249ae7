import * as TablerIcons from "@tabler/icons-react";

// Tabler icons for spaces - curated selection for personal expression
export const SPACE_ICONS = [
  // Work & Productivity
  "briefcase", "clipboard", "folder", "file-text", "chart-bar", "target", "bulb", "rocket",

  // Creative & Hobbies
  "palette", "brush", "camera", "music", "headphones", "microphone", "video", "photo",
  "paint", "pencil", "wand", "sparkles",

  // Home & Personal
  "home", "heart", "user", "users", "family", "baby-carriage", "paw", "plant",
  "coffee", "pizza", "chef-hat", "book", "books",

  // Travel & Adventure
  "plane", "car", "bike", "map", "compass", "mountain", "beach", "tent",
  "backpack", "luggage", "world", "location",

  // Health & Fitness
  "run", "swimming", "dumbbell", "yoga", "heart-rate-monitor", "apple",

  // Learning & Growth
  "school", "certificate", "trophy", "medal", "star", "flame", "trending-up",

  // Fun & Entertainment
  "device-gamepad", "puzzle", "dice", "balloon", "gift", "cake", "party-popper",

  // Nature & Outdoors
  "tree", "leaf", "flower", "sun", "moon", "cloud", "snowflake"
];

// Helper function to render Tabler icons
export const renderSpaceIcon = (iconName: string, className?: string) => {
  // Convert kebab-case to PascalCase for Tabler icon component names
  const componentName = iconName
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');

  const IconComponent = (TablerIcons as any)[`Icon${componentName}`];

  if (IconComponent) {
    return <IconComponent className={className || "h-5 w-5"} />;
  }

  // Fallback to clipboard icon if not found (includes old emoji icons)
  return <TablerIcons.IconClipboard className={className || "h-5 w-5"} />;
};
