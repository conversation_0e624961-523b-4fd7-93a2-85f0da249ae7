"use client";

import * as React from "react";
import { Plus, Search, Tag as TagIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ColorPicker } from "@/components/ui/color-picker";
import { TagPill } from "@/components/ui/tag-pill";
import { getRandomTagColor } from "@/lib/tag-colors";
import { useSmartTagSearch } from "@/hooks/use-smart-tag-search";
import { useMediaQuery } from "@/hooks/use-media-query";
import { MobileTagPicker } from "@/components/ui/mobile-tag-picker";
import type { Tag } from "@/lib/db";

export interface InlineTagPickerProps {
  selectedTags?: Tag[];
  availableTags: Tag[];
  onTagSelect: (tag: Tag) => void;
  onTagRemove?: (tag: Tag) => void;
  onTagCreate: (name: string, color: string) => Promise<Tag | null>;
  onSearchTags: (searchTerm: string) => Promise<Tag[]>;
  className?: string;
  disabled?: boolean;
  size?: "sm" | "default";
  isInModal?: boolean; // New prop to indicate this is being used within a modal
}

export function InlineTagPicker({
  selectedTags = [],
  availableTags,
  onTagSelect,
  onTagRemove,
  onTagCreate,
  onSearchTags,
  className,
  disabled = false,
  size = "sm",
  isInModal = false,
}: InlineTagPickerProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [isCreating, setIsCreating] = React.useState(false);
  const [newTagColor, setNewTagColor] = React.useState<string>(getRandomTagColor().value);
  const isDesktop = useMediaQuery("(min-width: 768px)");

  // Use smart search hook for optimal performance
  const { filteredTags, isSearching, searchTerm, setSearchTerm } = useSmartTagSearch({
    availableTags,
    selectedTags, // Pass selected tags to filter them out from available tags
    onSearchTags,
    clientSearchThreshold: 2, // Use client search for 1-2 characters
    debounceMs: 300,
  });



  const handleTagSelect = (tag: Tag) => {
    onTagSelect(tag);
    setSearchTerm("");
    setIsCreating(false);
    // Keep popover open for multiple selections
  };

  const handleCreateTag = async () => {
    if (!searchTerm.trim()) return;

    setIsCreating(true);
    try {
      const newTag = await onTagCreate(searchTerm.trim(), newTagColor);
      if (newTag) {
        onTagSelect(newTag);
        setSearchTerm("");
        setNewTagColor(getRandomTagColor().value);
        // Keep popover open for multiple selections
      }
    } catch (error) {
      console.error('Error creating tag:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && searchTerm.trim()) {
      e.preventDefault();

      // If there's an exact match, select it
      const exactMatch = filteredTags.find(
        tag => tag.name.toLowerCase() === searchTerm.toLowerCase()
      );

      if (exactMatch) {
        handleTagSelect(exactMatch);
      } else {
        // Create new tag
        handleCreateTag();
      }
    } else if (e.key === 'Escape') {
      setIsOpen(false);
      setSearchTerm("");
    }
  };

  const showCreateOption = searchTerm.trim() &&
    !filteredTags.some(tag => tag.name.toLowerCase() === searchTerm.toLowerCase());

  // Mobile version
  if (!isDesktop) {
    return (
      <>
        <Button
          data-inline-tag-picker
          type="button"
          variant="outline"
          size={size}
          className={cn(
            "rounded-full border-2 text-muted-foreground hover:text-foreground hover:border-foreground/50 transition-colors",
            size === "sm" ? "px-2 py-0.5 text-xs h-6" : "px-2.5 py-1 text-xs h-7",
            disabled && "opacity-50 cursor-not-allowed",
            className
          )}
          disabled={disabled}
          onClick={() => setIsOpen(true)}
        >
          <Plus className={cn(
            "mr-1",
            size === "sm" ? "h-2.5 w-2.5" : "h-3 w-3"
          )} />
          Tags
        </Button>

        <MobileTagPicker
          selectedTags={selectedTags}
          availableTags={availableTags}
          onTagSelect={(tag) => {
            onTagSelect(tag);
            // Keep modal open for multiple selections
          }}
          onTagRemove={onTagRemove || (() => {})}
          onTagCreate={onTagCreate}
          onSearchTags={onSearchTags}
          placeholder="Search or create tags..."
          disabled={disabled}
          open={isOpen}
          onOpenChange={setIsOpen}
          isStackedModal={isInModal}
        />
      </>
    );
  }

  // Desktop version
  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          data-inline-tag-picker
          type="button"
          variant="outline"
          size={size}
          className={cn(
            "rounded-full border-2 text-muted-foreground hover:text-foreground hover:border-foreground/50 transition-colors",
            size === "sm" ? "px-2 py-0.5 text-xs h-6" : "px-2.5 py-1 text-xs h-7",
            disabled && "opacity-50 cursor-not-allowed",
            className
          )}
          disabled={disabled}
        >
          <Plus className={cn(
            "mr-1",
            size === "sm" ? "h-2.5 w-2.5" : "h-3 w-3"
          )} />
          Tags
        </Button>
      </PopoverTrigger>

      <PopoverContent className="w-72 p-0" align="start">
        <div className="p-3 space-y-3">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search or create tags..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={handleKeyDown}
              className="pl-9"
              autoFocus={false}
            />
          </div>

          {/* Selected Tags */}
          {selectedTags.length > 0 && (
            <div className="border-b pb-3">
              <h3 className="text-sm font-medium text-muted-foreground mb-2">Selected</h3>
              <div className="flex flex-wrap gap-1.5">
                {selectedTags.map((tag) => (
                  <TagPill
                    key={tag.id}
                    tag={tag}
                    onRemove={onTagRemove ? () => onTagRemove(tag) : undefined}
                    size="sm"
                    showRemove={!!onTagRemove}
                    allowInlineEdit={false}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Create New Tag Option */}
          {showCreateOption && (
            <div className="border-b pb-3">
              <div className="flex items-center gap-2 p-2 rounded-md hover:bg-muted/50">
                <ColorPicker
                  value={newTagColor}
                  onChange={setNewTagColor}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="flex-1 justify-start h-auto p-1"
                  onClick={handleCreateTag}
                  disabled={isCreating}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Create "{searchTerm}"
                </Button>
              </div>
            </div>
          )}

          {/* Available Tags */}
          <div>
            {filteredTags.length > 0 && (
              <h3 className="text-sm font-medium text-muted-foreground mb-2">
                {searchTerm ? 'Search Results' : 'Available Tags'}
              </h3>
            )}
            <div className="max-h-48 overflow-y-auto">
              {isSearching ? (
                <div className="text-center py-4 text-muted-foreground">
                  Searching...
                </div>
              ) : filteredTags.length > 0 ? (
                <div className="space-y-1">
                  {filteredTags.map((tag) => (
                    <Button
                      key={tag.id}
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start h-auto p-2"
                      onClick={() => handleTagSelect(tag)}
                    >
                      <div
                        className="w-3 h-3 rounded-full mr-2 flex-shrink-0"
                        style={{ backgroundColor: tag.color }}
                      />
                      <span className="truncate">{tag.name}</span>
                    </Button>
                  ))}
                </div>
              ) : searchTerm ? (
                <div className="text-center py-4 text-muted-foreground">
                  No tags found
                </div>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  Start typing to search or create tags
                </div>
              )}
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
