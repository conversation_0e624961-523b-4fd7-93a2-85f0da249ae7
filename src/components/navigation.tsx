"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { UserMenu } from "@/components/user-menu";
import { useListColor } from "@/contexts/list-color-context";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { SkeletonNavigation } from "@/components/ui/skeleton";
import { renderSpaceIcon } from "@/lib/space-icons";



const routes = [
  {
    href: "/dashboard",
    label: "Dashboard",
  },
  {
    href: "/tasks",
    label: "Tasks",
  },
  {
    href: "/calendar",
    label: "Calendar",
  },
];

interface NavigationProps {
  isLoading?: boolean;
  showSkeleton?: boolean;
  currentSpace?: { id: string; name: string; icon?: string | null } | null;
  onSpaceClick?: () => void;
}

export function Navigation({
  isLoading = false,
  showSkeleton = false,
  currentSpace = null,
  onSpaceClick
}: NavigationProps = {}) {
  const [currentPath, setCurrentPath] = useState("");
  const [isScrollingDown, setIsScrollingDown] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [headerHeight, setHeaderHeight] = useState(48); // Default height (h-12)
  const pathname = usePathname();
  const { currentListColor, setCurrentListColor } = useListColor();
  const headerRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Only set the pathname on the client side
    setCurrentPath(pathname || "");
  }, [pathname]);

  // Measure header height on mount and resize
  useEffect(() => {
    const measureHeaderHeight = () => {
      if (headerRef.current) {
        const height = headerRef.current.offsetHeight;
        setHeaderHeight(height);
      }
    };

    measureHeaderHeight();
    window.addEventListener('resize', measureHeaderHeight);

    return () => {
      window.removeEventListener('resize', measureHeaderHeight);
    };
  }, []);

  // Scroll detection logic for hide/show behavior
  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const currentScrollY = window.scrollY;
          const scrollDelta = currentScrollY - lastScrollY;

          // Only trigger on mobile devices
          const isMobile = window.innerWidth < 768;
          if (!isMobile) {
            if (isScrollingDown) setIsScrollingDown(false);
            setLastScrollY(currentScrollY);
            ticking = false;
            return;
          }

          // Determine scroll direction with threshold to prevent jitter
          const scrollThreshold = 8;
          const minScrollY = 100; // Minimum scroll before hiding header

          if (Math.abs(scrollDelta) > scrollThreshold) {
            if (scrollDelta > 0 && currentScrollY > minScrollY) {
              // Scrolling down and past initial threshold
              setIsScrollingDown(true);
            } else if (scrollDelta < 0 || currentScrollY <= 50) {
              // Scrolling up or near top
              setIsScrollingDown(false);
            }
          }

          setLastScrollY(currentScrollY);
          ticking = false;
        });
        ticking = true;
      }
    };

    // Add scroll listener with passive option for better performance
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [lastScrollY, isScrollingDown]);

  // Reset list color context when navigating away from tasks page
  useEffect(() => {
    if (pathname && pathname !== "/tasks") {
      setCurrentListColor(null);
    }
  }, [pathname, setCurrentListColor]);

  // Find the current route to display on mobile
  const currentRoute = routes.find((route) => route.href === currentPath);

  // Get logo styles based on current list color
  const getLogoStyles = () => {
    if (!currentListColor) {
      // Plain white for colorless lists with subtle glow
      return {
        backgroundColor: '#ffffff',
        boxShadow: '0 0 8px rgba(255, 255, 255, 0.3), 0 0 16px rgba(255, 255, 255, 0.1)',
      };
    }

    // Apply solid color background based on current list color with subtle glow
    return {
      backgroundColor: currentListColor,
      boxShadow: `0 0 8px ${currentListColor}40, 0 0 16px ${currentListColor}20`,
    };
  };

  // Show skeleton loading state
  if (showSkeleton) {
    return (
      <header
        ref={headerRef}
        className="sticky top-0 z-50 w-full bg-background"
        style={{
          willChange: 'transform',
          transform: isScrollingDown ? `translateY(-${headerHeight}px)` : 'translateY(0)',
          transition: 'transform 350ms cubic-bezier(0.4, 0, 0.2, 1)',
        }}
      >
        <div className="container-max-width mx-auto">
          <SkeletonNavigation />
        </div>
      </header>
    );
  }

  return (
    <header
      ref={headerRef}
      className="sticky top-0 z-50 w-full bg-background"
      style={{
        willChange: 'transform',
        transform: isScrollingDown ? `translateY(-${headerHeight}px)` : 'translateY(0)',
        transition: 'transform 350ms cubic-bezier(0.4, 0, 0.2, 1)',
      }}
    >
      <div className="container-max-width flex h-12 items-center justify-between px-3 mx-auto relative">
        {/* App logo */}
        <div className="flex items-center">
          <Link href="/tasks" className="flex items-center space-x-2 p-2">
            <div
              className="relative w-16 h-16 md:w-20 md:h-20"
              style={{
                ...getLogoStyles(),
                WebkitMask: 'url(/NeoTask_Logo_white.webp) no-repeat center/contain',
                mask: 'url(/NeoTask_Logo_white.webp) no-repeat center/contain',
              }}
            />
          </Link>
          <nav className="hidden md:flex items-center ml-6 space-x-6 text-sm font-medium">
            {routes.map((route) => (
              <Link
                key={route.href}
                href={route.href}
                className={`transition-colors hover:text-foreground/80 ${
                  currentPath === route.href
                    ? "text-foreground"
                    : "text-foreground/60"
                }`}
              >
                {route.label}
              </Link>
            ))}
          </nav>
        </div>

        {/* Centered page title on mobile */}
        {currentRoute && (
          <div className="absolute left-0 right-0 mx-auto w-fit md:hidden">
            {currentRoute.href === "/tasks" && currentSpace ? (
              <button
                type="button"
                onClick={onSpaceClick}
                className="glass-card flex items-center gap-2 font-medium hover:opacity-80 transition-all duration-200 px-3 py-1.5 rounded-lg border"
                data-glass-intensity="subtle"
              >
                {renderSpaceIcon(currentSpace.icon || "clipboard", "h-5 w-5")}
                <span>{currentSpace.name}</span>
              </button>
            ) : (
              <h1 className="font-medium">{currentRoute.label}</h1>
            )}
          </div>
        )}

        {/* User menu always on the right */}
        <div className="flex items-center gap-4 z-10">
          {/* Desktop space selector - only show on tasks page when space exists */}
          {currentPath === "/tasks" && currentSpace && (
            <button
              type="button"
              onClick={onSpaceClick}
              className="glass-card hidden md:flex items-center gap-2 font-medium hover:opacity-80 transition-all duration-200 text-foreground px-3 py-1.5 rounded-lg border"
              data-glass-intensity="subtle"
            >
              {renderSpaceIcon(currentSpace.icon || "clipboard", "h-5 w-5")}
              <span>{currentSpace.name}</span>
            </button>
          )}

          {isLoading ? (
            <div className="flex items-center gap-2">
              <LoadingSpinner size="sm" />
              <UserMenu />
            </div>
          ) : (
            <UserMenu />
          )}
        </div>
      </div>
    </header>
  );
}
